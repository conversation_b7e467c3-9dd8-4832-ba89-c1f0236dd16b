"""
Configurações Específicas - Unidade de Negócio Seguros
"""

from salesforce_integration.config import SALESFORCE_CONFIG

# =============================================================================
# CONFIGURAÇÕES SALESFORCE MARKETING CLOUD - SEGUROS
# =============================================================================

SALESFORCE_SEGUROS_CONFIG = {
    'client_id': 'ljmwlofrhxbmnhdgvyivwpy1',  # Específico para seguros
    'client_secret': 'N5o7S3i9HXKEz0DWjdnhVn51',  # Específico para seguros
    'auth_uri': SALESFORCE_CONFIG['auth_uri'],  # Mesmo ambiente
    'rest_uri': SALESFORCE_CONFIG['rest_uri'],  # Mesmo ambiente
    'timeout': SALESFORCE_CONFIG['timeout'],
    'batch_size': SALESFORCE_CONFIG['batch_size'],
    'rate_limit': SALESFORCE_CONFIG['rate_limit'],
    'retry_attempts': SALESFORCE_CONFIG['retry_attempts'],
    'retry_delay': SALESFORCE_CONFIG['retry_delay'],
    'exponential_backoff': SALESFORCE_CONFIG['exponential_backoff'],
    'polling_interval': SALESFORCE_CONFIG['polling_interval'],
    'max_polling_time': SALESFORCE_CONFIG['max_polling_time'],
}

# =============================================================================
# DATA EXTENSIONS - SEGUROS (NOVAS)
# =============================================================================

DATA_EXTENSIONS_SEGUROS = {
    'tb_clientes': {
        'external_key': 'B2B7ACFF-D5C8-4C86-B04A-DB4FBBA9198E',  # Novo para seguros
        'required_fields': ['cnpjcpf', 'email'],
        'estimated_records': 20000,  # Estimativa menor para seguros
        'batch_count': 10,
        'priority': 2,
    },
    'tb_leads': {
        'external_key': 'EC0B7BFF-EC89-4A4D-914B-749F14B6F861',  # Novo para seguros
        'required_fields': ['cnpjcpf', 'dt_simulacao'],
        'estimated_records': 0,  # Variable
        'batch_count': 0,  # Dynamic
        'priority': 3,
    },
    'tb_produtos': {
        'external_key': 'FCC1DCA7-D286-458D-BDCC-D050C1BA61A8',  # Novo para seguros
        'required_fields': ['id_produto'],
        'estimated_records': 5000,  # Menor volume para seguros
        'batch_count': 3,
        'priority': 1,
    },
    'tb_propostas': {
        'external_key': '36B6F6B2-ECC3-4550-828C-5BF3B12FCBCA',  # Novo para seguros
        'required_fields': ['idproposta', 'email'],
        'estimated_records': 100000,  # Menor volume para seguros
        'batch_count': 50,
        'priority': 4,
    }
}

# =============================================================================
# FONTES DE DADOS - SEGUROS
# =============================================================================

SOURCES_SEGUROS = ['quiver']  # Apenas Quiver para seguros

# =============================================================================
# CONFIGURAÇÃO DA DAG - SEGUROS
# =============================================================================

DAG_SEGUROS_CONFIG = {
    'dag_config': {
        'schedule_interval': '0 9 * * *',  # 9h da manhã (1h após consórcio)
        'default_args': {
            'email': ['<EMAIL>'],  # Email específico de seguros
        }
    },
    'salesforce_config': SALESFORCE_SEGUROS_CONFIG,
    'data_extensions': DATA_EXTENSIONS_SEGUROS,
    'sources': SOURCES_SEGUROS,
    
    # Tarefas de extração específicas de seguros (apenas Quiver)
    'extraction_tasks': {
        'quiver_clients': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_quiver_clients_individual_task',
            'doc_md': 'Extrai clientes Quiver para seguros',
            'priority_weight': 8,
            'timeout_minutes': 10
        },
        'quiver_leads': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_quiver_leads_individual_task',
            'doc_md': 'Extrai leads Quiver para seguros',
            'priority_weight': 7,
            'timeout_minutes': 8
        },
        'quiver_products': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_quiver_products_individual_task',
            'doc_md': 'Extrai produtos Quiver para seguros',
            'priority_weight': 6,
            'timeout_minutes': 6
        },
        'quiver_proposals': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_extract_quiver_proposals_individual_task',
            'doc_md': 'Extrai propostas Quiver para seguros',
            'priority_weight': 9,
            'timeout_minutes': 12
        }
    },
    
    # Tarefa de consolidação
    'consolidate_callable': 'salesforce_integration.airflow_task_wrappers.airflow_consolidate_table_extractions_task',
    
    # Tarefas de transformação (mesmas funções, mas com contexto de seguros)
    'transformation_tasks': {
        'produtos': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_transform_produtos_task',
            'doc_md': 'Transformação produtos para seguros',
            'priority_weight': 10,
            'timeout_minutes': 5
        },
        'clientes': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_transform_clientes_task',
            'doc_md': 'Transformação clientes para seguros',
            'priority_weight': 9,
            'timeout_minutes': 8
        },
        'leads': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_transform_leads_task',
            'doc_md': 'Transformação leads para seguros',
            'priority_weight': 8,
            'timeout_minutes': 6
        },
        'propostas': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_transform_propostas_task',
            'doc_md': 'Transformação propostas para seguros',
            'priority_weight': 7,
            'timeout_minutes': 10
        }
    },
    
    # Tarefas de carregamento (mesmas funções, mas com contexto de seguros)
    'load_tasks': {
        'produtos': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_load_produtos_parallel_task',
            'doc_md': 'Carregamento produtos para seguros',
            'priority_weight': 10,
            'timeout_minutes': 5
        },
        'clientes': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_load_clientes_parallel_task',
            'doc_md': 'Carregamento clientes para seguros',
            'priority_weight': 9,
            'timeout_minutes': 8
        },
        'leads': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_load_leads_parallel_task',
            'doc_md': 'Carregamento leads para seguros',
            'priority_weight': 8,
            'timeout_minutes': 15
        },
        'propostas': {
            'callable': 'salesforce_integration.airflow_task_wrappers.airflow_load_propostas_parallel_task',
            'doc_md': 'Carregamento propostas para seguros',
            'priority_weight': 7,
            'timeout_minutes': 15
        }
    },
    
    # Tarefa de relatório
    'report_callable': 'salesforce_integration.airflow_task_wrappers.airflow_generate_failure_report_task'
}